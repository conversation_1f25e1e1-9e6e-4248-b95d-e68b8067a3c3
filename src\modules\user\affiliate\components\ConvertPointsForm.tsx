import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Input,
  Button,
  Typography,
  IconCard,
} from '@/shared/components/common';
import { useConvertPoints } from '../hooks';
import { convertPointsSchema, ConvertPointsData } from '../schemas';
import { AFFILIATE_CONSTANTS } from '../constants';
import { formatCurrency } from '@/shared/utils/number-format.utils';

interface ConvertPointsFormProps {
  onBack: () => void;
  currentPoints: number;
}

/**
 * Form đổi points thành tiền
 */
const ConvertPointsForm: React.FC<ConvertPointsFormProps> = ({
  onBack,
  currentPoints,
}) => {
  const { t } = useTranslation(['userAffiliate', 'common']);
  const [formData, setFormData] = useState<ConvertPointsData>({
    points: 0,
  });
  const [errors, setErrors] = useState<Partial<Record<keyof ConvertPointsData, string>>>({});

  const convertPointsMutation = useConvertPoints();

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    try {
      // Validate form data
      const validatedData = convertPointsSchema.parse(formData);

      // Check if points exceed current points
      if (validatedData.points > currentPoints) {
        setErrors({ points: 'Số point không được vượt quá số point hiện có' });
        return;
      }

      // Submit convert points request
      await convertPointsMutation.mutateAsync(validatedData.points);

      // Reset form and go back
      setFormData({ points: 0 });
      setErrors({});
      onBack();

      // TODO: Show success toast
    } catch (error) {
      console.error('Error converting points:', error);
      // Handle generic error
      setErrors({ points: 'Có lỗi xảy ra, vui lòng thử lại' });
    }
  };

  const handlePointsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || 0;
    setFormData({ points: value });
  };

  const handleMaxPoints = () => {
    setFormData({ points: currentPoints });
  };

  const convertedAmount = formData.points * AFFILIATE_CONSTANTS.POINTS_TO_MONEY_RATE;

  return (
    <div className="space-y-4">
      {/* Header with back button */}
      <div className="flex items-center justify-between mb-4">
        <Typography variant="h5">
          {t('userAffiliate:convertPoints.title')}
        </Typography>
        <IconCard
          icon="arrow-left"
          size="sm"
          variant="ghost"
          onClick={onBack}
          title="Quay lại"
        />
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="mb-4">
          <Typography variant="body2" color="muted">
            {t('userAffiliate:convertPoints.currentPoints')}: {currentPoints.toLocaleString()} points
          </Typography>
          <Typography variant="caption" color="muted">
            {t('userAffiliate:convertPoints.rate')}: 1 point = {formatCurrency(AFFILIATE_CONSTANTS.POINTS_TO_MONEY_RATE)}
          </Typography>
          <Typography variant="caption" color="muted">
            {t('userAffiliate:convertPoints.minPoints')}: {AFFILIATE_CONSTANTS.MIN_POINTS_CONVERSION} points
          </Typography>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium">
            {t('userAffiliate:convertPoints.points')} <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2">
            <Input
              type="number"
              value={formData.points || ''}
              onChange={handlePointsChange}
              placeholder={t('userAffiliate:convertPoints.pointsPlaceholder')}
              min={AFFILIATE_CONSTANTS.MIN_POINTS_CONVERSION}
              max={currentPoints}
            />
            <Button
              type="button"
              variant="outline"
              onClick={handleMaxPoints}
              className="whitespace-nowrap"
            >
              {t('userAffiliate:convertPoints.maxPoints')}
            </Button>
          </div>
          {errors.points && (
            <Typography variant="caption" color="danger">
              {errors.points}
            </Typography>
          )}
        </div>

        {formData.points > 0 && (
          <div className="p-4 bg-gray-50 rounded-md">
            <Typography variant="body2">
              {t('userAffiliate:convertPoints.convertedAmount')}: {formatCurrency(convertedAmount)}
            </Typography>
          </div>
        )}

        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onBack}
            disabled={convertPointsMutation.isPending}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={convertPointsMutation.isPending}
          >
            {t('userAffiliate:convertPoints.submit')}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ConvertPointsForm;
