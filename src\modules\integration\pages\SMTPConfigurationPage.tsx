import React, { useState, useRef, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import {
  Card,
  Typography,
  Button,
  Input,
  Textarea,
  FormItem,
  Form,
  Icon,
  Alert,
  ResponsiveGrid,
} from '@/shared/components/common';
import Toggle from '@/shared/components/common/Toggle';
import { emailServerConfigurationSchema } from '../email/schemas';
import {
  EmailServerConfiguration,
  TestEmailServerWithConfigDto,
  EmailServerConfigDto,
} from '../email/types';
import { useTestEmailServerWithConfig } from '../email/hooks';

// Định nghĩa kiểu dữ liệu cho form
export type SMTPFormValues = z.infer<typeof emailServerConfigurationSchema>;

interface SMTPConfigurationPageProps {
  /**
   * Dữ liệu ban đầu cho form (khi chỉnh sửa)
   */
  initialData?: EmailServerConfiguration | null;

  /**
   * <PERSON>à<PERSON> xử lý khi submit form
   */
  onSubmit?: (values: Record<string, unknown>) => void;

  /**
   * Chế độ chỉ đọc
   */
  readOnly?: boolean;
}

/**
 * Trang cấu hình SMTP
 */
const SMTPConfigurationPage: React.FC<SMTPConfigurationPageProps> = ({
  initialData,
  onSubmit,
  readOnly = false,
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const formRef = useRef(null);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const testEmailWithConfigMutation = useTestEmailServerWithConfig();
  const isEditMode = !!initialData;

  // Test connection states
  const [testEmail, setTestEmail] = useState('');

  // Validation email helper
  const isValidEmail = useCallback((email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }, []);

  // Memoize defaultValues để tránh re-render không cần thiết
  const defaultValues = useMemo(() => {
    return initialData
      ? {
          serverName: initialData.serverName,
          host: initialData.host,
          port: initialData.port,
          username: initialData.username,
          password: '', // Không hiển thị password cũ
          useSsl: initialData.useSsl,
          useStartTls: initialData.useStartTls,
          additionalSettings: initialData.additionalSettings
            ? JSON.stringify(initialData.additionalSettings, null, 2)
            : '',
          isActive: initialData.isActive,
        }
      : {
          serverName: '',
          host: '',
          port: 587,
          username: '',
          password: '',
          useSsl: false,
          useStartTls: true,
          additionalSettings: '',
          isActive: true,
        };
  }, [initialData]);

  // Memoize handlers để tránh re-render
  const handleFormSubmit = useCallback(
    (values: Record<string, unknown>) => {
      setIsSubmitting(true);
      try {
        // Parse additionalSettings từ JSON string nếu có
        const processedValues = {
          ...values,
          additionalSettings: values.additionalSettings
            ? JSON.parse(values.additionalSettings as string)
            : undefined,
        };
        
        if (onSubmit) {
          onSubmit(processedValues);
        } else {
          // Default behavior - log values
          console.log('SMTP Configuration:', processedValues);
          alert(t('integration:smtp.saveSuccess', 'Cấu hình SMTP đã được lưu thành công!'));
        }
      } catch (error) {
        console.error('Error saving SMTP configuration:', error);
        alert(t('integration:smtp.saveError', 'Có lỗi xảy ra khi lưu cấu hình SMTP'));
      } finally {
        setIsSubmitting(false);
      }
    },
    [onSubmit, t]
  );

  // Xử lý test kết nối
  const handleTestConnection = useCallback(async () => {
    if (!testEmail) {
      alert(t('integration:email.testEmailRequired', 'Vui lòng nhập email để test'));
      return;
    }

    // Validate email format
    if (!isValidEmail(testEmail)) {
      alert(t('integration:email.invalidEmailFormat', 'Định dạng email không hợp lệ'));
      return;
    }

    // Lấy dữ liệu form hiện tại
    const formData = formRef.current
      ? (formRef.current as { getValues: () => SMTPFormValues }).getValues()
      : ({} as SMTPFormValues);

    // Kiểm tra các trường bắt buộc
    if (!formData.host || !formData.username || !formData.password) {
      alert(
        t(
          'integration:email.fillRequiredFields',
          'Vui lòng điền đầy đủ thông tin Host, Username và Password'
        )
      );
      return;
    }

    try {
      const testData = {
        recipientEmail: testEmail,
        subject: 'Test Email từ RedAI - Kiểm tra cấu hình SMTP',
      };

      let additionalSettings = {};
      try {
        if (formData.additionalSettings && formData.additionalSettings.trim()) {
          additionalSettings = JSON.parse(formData.additionalSettings);
        }
      } catch (error) {
        console.warn('Invalid JSON in additionalSettings, using empty object:', error);
      }

      const emailServerConfig: EmailServerConfigDto = {
        serverName: formData.serverName || 'Test SMTP Configuration',
        host: formData.host,
        port: formData.port || 587,
        username: formData.username,
        password: formData.password,
        useSsl: formData.useSsl !== undefined ? formData.useSsl : false,
        useStartTls: formData.useStartTls !== undefined ? formData.useStartTls : true,
        additionalSettings,
      };

      const testEmailServerWithConfigData: TestEmailServerWithConfigDto = {
        emailServerConfig,
        testInfo: testData,
      };

      const testResponse = await testEmailWithConfigMutation.mutateAsync(
        testEmailServerWithConfigData
      );

      setTestResult({
        success: testResponse.result.success,
        message: testResponse.result.message || 'Test email đã được gửi thành công!',
      });
    } catch (error: unknown) {
      console.error('Error testing SMTP connection:', error);
      setTestResult({
        success: false,
        message:
          (error as { response?: { data?: { message?: string } } })?.response?.data?.message ||
          t('integration:email.testError', 'Lỗi khi kiểm tra kết nối SMTP'),
      });
    }
  }, [
    testEmail,
    t,
    testEmailWithConfigMutation,
    isValidEmail,
  ]);

  return (
    <div className="w-full bg-background text-foreground">
        <Card>
          <Form
            schema={emailServerConfigurationSchema}
            onSubmit={handleFormSubmit}
            className="space-y-6"
            defaultValues={defaultValues}
            ref={formRef}
            useDefaultValuesOnce={true}
          >
            <FormItem
              name="serverName"
              label={t('integration:smtp.form.fields.serverName', 'Tên máy chủ')}
              required
            >
              <Input
                placeholder={t('integration:smtp.form.placeholders.serverName', 'Nhập tên máy chủ SMTP')}
                disabled={readOnly || isSubmitting}
                fullWidth
              />
            </FormItem>

            <FormItem 
              name="host" 
              label={t('integration:smtp.form.fields.host', 'Máy chủ SMTP')} 
              required
            >
              <Input
                placeholder={t('integration:smtp.form.placeholders.host', 'smtp.gmail.com')}
                disabled={readOnly || isSubmitting}
                fullWidth
              />
            </FormItem>

            <FormItem 
              name="port" 
              label={t('integration:smtp.form.fields.port', 'Cổng')} 
              required
            >
              <Input
                type="number"
                placeholder={t('integration:smtp.form.placeholders.port', '587')}
                disabled={readOnly || isSubmitting}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="username"
              label={t('integration:smtp.form.fields.username', 'Tên đăng nhập')}
              required
            >
              <Input
                type="email"
                placeholder={t('integration:smtp.form.placeholders.username', '<EMAIL>')}
                disabled={readOnly || isSubmitting}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="password"
              label={t('integration:smtp.form.fields.password', 'Mật khẩu')}
              required={!isEditMode}
            >
              <Input
                type="password"
                placeholder={t('integration:smtp.form.placeholders.password', 'Nhập mật khẩu hoặc app password')}
                disabled={readOnly || isSubmitting}
                fullWidth
              />
            </FormItem>

            {/* SSL/TLS Settings */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FormItem name="useSsl" label={t('integration:smtp.form.fields.useSsl', 'Sử dụng SSL')}>
                <Toggle disabled={readOnly || isSubmitting} label="" />
              </FormItem>

              <FormItem
                name="useStartTls"
                label={t('integration:smtp.form.fields.useStartTls', 'Sử dụng StartTLS')}
              >
                <Toggle disabled={readOnly || isSubmitting} label="" />
              </FormItem>

              <FormItem name="isActive" label={t('integration:smtp.form.fields.isActive', 'Kích hoạt')}>
                <Toggle disabled={readOnly || isSubmitting} label="" />
              </FormItem>
            </div>

            {/* Additional Settings */}
            <FormItem
              name="additionalSettings"
              label={t('integration:smtp.form.fields.additionalSettings', 'Cài đặt bổ sung (JSON)')}
            >
              <Textarea
                placeholder={t('integration:smtp.form.placeholders.additionalSettings', '{"timeout": 30000}')}
                disabled={readOnly || isSubmitting}
                rows={4}
                fullWidth
              />
            </FormItem>

            {/* Test Connection Section */}
            {!readOnly && (
              <div>
                <Typography variant="h6" className="mb-4">
                  {t('integration:smtp.form.test', 'Kiểm tra kết nối')}
                </Typography>

                <Typography variant="body2" className="text-muted-foreground mb-4">
                  {t(
                    'integration:smtp.form.testDescription',
                    'Nhập email để nhận email test từ cấu hình SMTP này'
                  )}
                </Typography>

                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      value={testEmail}
                      onChange={e => setTestEmail(e.target.value)}
                      leftIcon={<Icon name="mail" size="sm" />}
                      fullWidth
                      error={
                        testEmail && !isValidEmail(testEmail)
                          ? t('integration:email.invalidEmailFormat', 'Định dạng email không hợp lệ')
                          : undefined
                      }
                    />
                  </div>
                  <Button
                    type="button"
                    variant="primary"
                    leftIcon={<Icon name="send" size="sm" />}
                    onClick={handleTestConnection}
                    disabled={
                      !testEmail ||
                      !isValidEmail(testEmail) ||
                      testEmailWithConfigMutation.isPending
                    }
                    isLoading={testEmailWithConfigMutation.isPending}
                  >
                    {t('integration:smtp.actions.sendTest', 'Gửi test')}
                  </Button>
                </div>

                {/* Test Result */}
                {testResult && (
                  <div className="mt-4">
                    <Alert
                      type={testResult.success ? 'success' : 'error'}
                      title={testResult.success ? t('common:success') : t('common:error')}
                      message={testResult.message}
                      showIcon
                    />
                  </div>
                )}
              </div>
            )}

            {/* Form Actions */}
            {!readOnly && (
              <div className="flex justify-end space-x-4 pt-4">
                <Button type="submit" variant="primary" isLoading={isSubmitting}>
                  {t('integration:smtp.actions.save', 'Lưu cấu hình')}
                </Button>
              </div>
            )}
          </Form>
        </Card>
    </div>
  );
};

export default SMTPConfigurationPage;
