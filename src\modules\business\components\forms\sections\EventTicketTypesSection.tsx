import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  CollapsibleCard,
  Typography,
  FormItem,
  Input,
  Textarea,
  IconCard,
  AvatarImageUploader,
} from '@/shared/components/common';
import { EventTicketTypesSectionProps } from './event-product-form-types';

const EventTicketTypesSection: React.FC<EventTicketTypesSectionProps> = ({
  ticketTypes,
  handleAddTicketType,
  handleRemoveTicketType,
  handleUpdateTicketType,
  handleTicketImageUpload,
  handleRemoveTicketImage,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Tính tổng số vé
  const totalTickets = ticketTypes.reduce((sum, ticket) => sum + (ticket.totalTickets || 0), 0);
  
  // Tính giá thấp nhất và cao nhất
  const prices = ticketTypes.map(ticket => ticket.price).filter(price => price > 0);
  const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
  const maxPrice = prices.length > 0 ? Math.max(...prices) : 0;

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="font-medium">
          {ticketTypes.length > 0
            ? `3. ${t('business:product.form.sections.eventTicketTypes', 'Loại vé')} (${ticketTypes.length})`
            : `3. ${t('business:product.form.sections.eventTicketTypes', 'Loại vé')}`
          }
        </Typography>
      }
      defaultOpen={true}
      className="mb-4"
    >
      <div className="space-y-4">
        {/* Thông tin tổng quan */}
        {ticketTypes.length > 0 && (
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
            <Typography variant="body2" className="font-medium text-green-800 dark:text-green-200 mb-2">
              {t('business:product.form.eventProduct.ticketSummary', 'Tổng quan loại vé')}
            </Typography>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex justify-between">
                <span className="text-green-700 dark:text-green-300">
                  {t('business:product.form.eventProduct.totalTicketTypes', 'Số loại vé')}:
                </span>
                <span className="font-medium">{ticketTypes.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-700 dark:text-green-300">
                  {t('business:product.form.eventProduct.totalTickets', 'Tổng số vé')}:
                </span>
                <span className="font-medium">{totalTickets.toLocaleString('vi-VN')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-700 dark:text-green-300">
                  {t('business:product.form.eventProduct.priceRange', 'Khoảng giá')}:
                </span>
                <span className="font-medium">
                  {minPrice === maxPrice 
                    ? `${minPrice.toLocaleString('vi-VN')} VND`
                    : `${minPrice.toLocaleString('vi-VN')} - ${maxPrice.toLocaleString('vi-VN')} VND`
                  }
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Danh sách loại vé */}
        <div className="space-y-4">
          {ticketTypes.map((ticket, index) => (
            <div
              key={ticket.id}
              className="p-4 bg-gray-50 dark:bg-gray-800/30 rounded-lg border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center justify-between mb-4">
                <Typography variant="subtitle1" className="font-medium">
                  {t('business:product.form.eventProduct.ticketType', 'Loại vé')} #{index + 1}
                  {ticket.name && ` - ${ticket.name}`}
                </Typography>
                <div className="flex items-center gap-2">
                  {ticketTypes.length > 1 && (
                    <IconCard
                      icon="trash"
                      title={t('business:product.form.eventProduct.removeTicketType', 'Xóa loại vé')}
                      variant="danger"
                      size="sm"
                      onClick={() => handleRemoveTicketType(ticket.id)}
                      className="cursor-pointer"
                    />
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Tên loại vé */}
                <FormItem 
                  label={t('business:product.form.eventProduct.ticketName', 'Tên loại vé')} 
                  required
                >
                  <Input
                    fullWidth
                    value={ticket.name}
                    onChange={(e) => handleUpdateTicketType(ticket.id, 'name', e.target.value)}
                    placeholder={t('business:product.form.eventProduct.ticketNamePlaceholder', 'VD: Vé thường, Vé VIP')}
                  />
                </FormItem>

                {/* Giá vé */}
                <FormItem 
                  label={t('business:product.form.eventProduct.ticketPrice', 'Giá vé (VND)')} 
                  required
                >
                  <Input
                    fullWidth
                    type="number"
                    min="0"
                    value={ticket.price}
                    onChange={(e) => handleUpdateTicketType(ticket.id, 'price', Number(e.target.value))}
                    placeholder="0"
                  />
                </FormItem>

                {/* Tổng số vé */}
                <FormItem 
                  label={t('business:product.form.eventProduct.totalTickets', 'Tổng số vé')} 
                  required
                >
                  <Input
                    fullWidth
                    type="number"
                    min="1"
                    value={ticket.totalTickets || 1}
                    onChange={(e) => handleUpdateTicketType(ticket.id, 'totalTickets', Number(e.target.value))}
                    placeholder="1"
                  />
                </FormItem>

                {/* SKU */}
                <FormItem label={t('business:product.form.eventProduct.ticketSku', 'Mã SKU')}>
                  <Input
                    fullWidth
                    value={ticket.sku || ''}
                    onChange={(e) => handleUpdateTicketType(ticket.id, 'sku', e.target.value)}
                    placeholder={t('business:product.form.eventProduct.ticketSkuPlaceholder', 'Mã định danh vé')}
                  />
                </FormItem>

                {/* Số lượng tối thiểu */}
                <FormItem 
                  label={t('business:product.form.eventProduct.minQuantity', 'Số vé tối thiểu/đơn')} 
                  required
                >
                  <Input
                    fullWidth
                    type="number"
                    min="1"
                    value={ticket.minQuantityPerOrder || 1}
                    onChange={(e) => handleUpdateTicketType(ticket.id, 'minQuantityPerOrder', Number(e.target.value))}
                    placeholder="1"
                  />
                </FormItem>

                {/* Số lượng tối đa */}
                <FormItem 
                  label={t('business:product.form.eventProduct.maxQuantity', 'Số vé tối đa/đơn')} 
                  required
                >
                  <Input
                    fullWidth
                    type="number"
                    min="1"
                    value={ticket.maxQuantityPerOrder || 10}
                    onChange={(e) => handleUpdateTicketType(ticket.id, 'maxQuantityPerOrder', Number(e.target.value))}
                    placeholder="10"
                  />
                </FormItem>
              </div>

              {/* Mô tả loại vé */}
              <div className="mt-4">
                <FormItem label={t('business:product.form.eventProduct.ticketDescription', 'Mô tả loại vé')}>
                  <Textarea
                    fullWidth
                    rows={3}
                    value={ticket.description || ''}
                    onChange={(e) => handleUpdateTicketType(ticket.id, 'description', e.target.value)}
                    placeholder={t('business:product.form.eventProduct.ticketDescriptionPlaceholder', 'Mô tả chi tiết về loại vé này...')}
                  />
                </FormItem>
              </div>

              {/* Ảnh vé */}
              <div className="mt-4">
                <FormItem label={t('business:product.form.eventProduct.ticketImage', 'Ảnh vé')}>
                  <AvatarImageUploader
                    value={ticket.ticketImage || ''}
                    onChange={(imageUrl) => {
                      if (typeof imageUrl === 'string') {
                        handleUpdateTicketType(ticket.id, 'ticketImage', imageUrl);
                      } else if (imageUrl instanceof File) {
                        handleTicketImageUpload(ticket.id, imageUrl);
                      }
                    }}
                    onRemove={() => handleRemoveTicketImage(ticket.id)}
                    placeholder={t('business:product.form.eventProduct.ticketImagePlaceholder', 'Tải lên ảnh mẫu vé')}
                    className="w-32 h-20"
                  />
                </FormItem>
              </div>
            </div>
          ))}
        </div>

        {/* Nút thêm loại vé */}
        <div className="flex justify-center">
          <IconCard
            icon="plus"
            title={t('business:product.form.eventProduct.addTicketType', 'Thêm loại vé')}
            variant="primary"
            onClick={handleAddTicketType}
            className="cursor-pointer"
          />
        </div>

        {/* Thông tin hướng dẫn */}
        <div className="bg-gray-50 dark:bg-gray-800/30 p-3 rounded-lg">
          <Typography variant="caption" className="text-gray-600 dark:text-gray-400">
            {t('business:product.form.eventProduct.ticketTypesNote', 
              'Lưu ý: Mỗi sự kiện phải có ít nhất 1 loại vé. Bạn có thể tạo nhiều loại vé với giá và quyền lợi khác nhau.'
            )}
          </Typography>
        </div>
      </div>
    </CollapsibleCard>
  );
};

export default EventTicketTypesSection;
