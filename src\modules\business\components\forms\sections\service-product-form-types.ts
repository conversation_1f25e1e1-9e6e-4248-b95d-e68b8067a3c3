import { FileWithMetadata } from '@/shared/types/file.types';
import { PriceTypeEnum, ServiceProductConfig } from '@/modules/business/types/product.types';

// Interface cho trường tùy chỉnh đã chọn
export interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Interface cho form values
export interface ServiceProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string | number;
  salePrice?: string | number;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  // Service product specific fields
  serviceTime?: Date;
  serviceDuration?: string | number;
  serviceProvider?: string;
  serviceType?: string;
  serviceLocation?: string;
}

// Props cho các section components
export interface ServiceGeneralInfoSectionProps {
  tempTags: string[];
  setTempTags: (tags: string[]) => void;
}

export interface ServicePricingSectionProps {
  // Có thể tái sử dụng DigitalPricingSection
}

export interface ServiceInfoSectionProps {
  // Không cần props đặc biệt, sử dụng form context
}

export interface ServiceMediaSectionProps {
  mediaFiles: FileWithMetadata[];
  setMediaFiles: (files: FileWithMetadata[]) => void;
  tempTags: string[];
  setTempTags: (tags: string[]) => void;
}

export interface ServiceCustomFieldsSectionProps {
  productCustomFields: SelectedCustomField[];
  setProductCustomFields: (fields: SelectedCustomField[]) => void;
  handleToggleCustomFieldToProduct: (fieldId: number, fieldData?: Record<string, unknown>) => void;
  handleUpdateCustomFieldInProduct: (customFieldId: number, value: string | number | boolean) => void;
  handleRemoveCustomFieldFromProduct: (customFieldId: number) => void;
}
