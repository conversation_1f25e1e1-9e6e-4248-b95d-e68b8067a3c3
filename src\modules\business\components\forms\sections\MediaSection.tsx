import React from 'react';
import { useTranslation } from 'react-i18next';
import { Controller } from 'react-hook-form';
import {
  CollapsibleCard,
  Typography,
  FormItem,
} from '@/shared/components/common';
import { MediaSectionProps } from './product-form-types';
import { MultiFileUpload } from '@/modules/data/components';

const MediaSection: React.FC<MediaSectionProps> = ({
  mediaFiles,
  setMediaFiles,
  tempTags, // eslint-disable-line @typescript-eslint/no-unused-vars
  setTempTags, // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  const { t } = useTranslation(['business', 'common']);
  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="font-medium">
          {t('business:product.form.sections.media', '3. Hình ảnh sản phẩm')}
        </Typography>
      }
      defaultOpen={false}
      className="mb-4"
    >
      <div className="space-y-4">
        <FormItem name="media" label={t('business:product.form.media')}>
          <Controller
            name="media"
            render={({ field }) => (
              <MultiFileUpload
                accept="image/*"
                mediaOnly={true}
                placeholder={t('business:product.form.mediaPlaceholder', 'Kéo thả hoặc click để tải lên ảnh/video')}
                onChange={(files) => {
                  setMediaFiles(files);
                  field.onChange(files);
                }}
                value={mediaFiles}
                className="w-full"
              />
            )}
          />
        </FormItem>
      </div>
    </CollapsibleCard>
  );
};

export default MediaSection;
