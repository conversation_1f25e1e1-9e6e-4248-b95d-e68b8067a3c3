import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  CollapsibleCard,
  Typography,
  FormItem,
  Input,
  IconCard,
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { ComboProductsSectionProps } from './combo-product-form-types';

const ComboProductsSection: React.FC<ComboProductsSectionProps> = ({
  comboProducts,
  handleAddComboProduct,
  handleRemoveComboProduct,
  handleUpdateComboProduct,
  loadProducts,
  loadedProducts,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Xử lý chọn sản phẩm từ AsyncSelectWithPagination
  const handleProductSelect = (value: string | number | string[] | number[] | undefined) => {
    if (!value || (Array.isArray(value) && value.length === 0)) {
      return;
    }

    // Get the first value if it's an array
    const productId = Array.isArray(value) ? value[0] : value;
    if (!productId) return;
    const productIdStr = productId.toString();

    const productData = loadedProducts.get(productIdStr);
    if (productData) {
      handleAddComboProduct(Number(productId), productData.name || 'N/A');
    }
  };

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="font-medium">
          {comboProducts.length > 0
            ? `3. ${t('business:product.form.sections.comboProducts', 'Sản phẩm trong combo')} (${comboProducts.length})`
            : `3. ${t('business:product.form.sections.comboProducts', 'Sản phẩm trong combo')}`
          }
        </Typography>
      }
      defaultOpen={true}
      className="mb-4"
    >
      <div className="space-y-4">
        {/* Tìm kiếm và thêm sản phẩm */}
        <FormItem label={t('business:product.form.comboProduct.searchProduct', 'Tìm kiếm sản phẩm')}>
          <AsyncSelectWithPagination
            placeholder={t('business:product.form.comboProduct.searchProductPlaceholder', 'Nhập tên sản phẩm để tìm kiếm...')}
            loadOptions={loadProducts}
            onChange={handleProductSelect}
            value={undefined} // Reset sau khi chọn
            isClearable
            isSearchable
            className="w-full"
          />
        </FormItem>

        {/* Danh sách sản phẩm đã chọn */}
        {comboProducts.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <Typography variant="body2">
              {t('business:product.form.comboProduct.noProducts', 'Chưa có sản phẩm nào trong combo. Tìm kiếm và chọn sản phẩm ở trên.')}
            </Typography>
          </div>
        ) : (
          <div className="space-y-3">
            <Typography variant="body2" className="font-medium">
              {t('business:product.form.comboProduct.selectedProducts', 'Sản phẩm đã chọn')}:
            </Typography>
            
            {comboProducts.map((item) => {
              const productData = loadedProducts.get(item.productId.toString());
              const originalPrice = productData ? (productData.price?.salePrice || productData.price?.listPrice || 0) : 0;
              
              return (
                <div
                  key={item.productId}
                  className="flex items-center p-4 bg-gray-50 dark:bg-gray-800/30 rounded-lg border border-gray-200 dark:border-gray-700"
                >
                  <div className="flex-grow">
                    <div className="flex items-center justify-between mb-2">
                      <Typography variant="body2" className="font-medium">
                        {item.productName}
                      </Typography>
                      <div
                        onClick={() => handleRemoveComboProduct(item.productId)}
                        className="cursor-pointer"
                      >
                        <IconCard
                          icon="trash"
                          title={t('business:product.form.comboProduct.removeProduct', 'Xóa sản phẩm')}
                          variant="danger"
                          size="sm"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      <FormItem label={t('business:product.form.comboProduct.quantity', 'Số lượng')} required>
                        <Input
                          type="number"
                          min="1"
                          value={item.quantity}
                          onChange={(e) => handleUpdateComboProduct(item.productId, 'quantity', Number(e.target.value))}
                          fullWidth
                        />
                      </FormItem>
                      
                      <FormItem label={t('business:product.form.comboProduct.originalPrice', 'Giá gốc')}>
                        <Input
                          value={`${originalPrice.toLocaleString('vi-VN')} VND`}
                          disabled
                          fullWidth
                        />
                      </FormItem>
                      
                      <FormItem label={t('business:product.form.comboProduct.totalPrice', 'Thành tiền')}>
                        <Input
                          value={`${(originalPrice * item.quantity).toLocaleString('vi-VN')} VND`}
                          disabled
                          fullWidth
                        />
                      </FormItem>
                    </div>
                    
                    {item.discountPercent !== undefined && (
                      <div className="mt-2">
                        <FormItem label={t('business:product.form.comboProduct.discountPercent', 'Giảm giá (%)')}>
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            value={item.discountPercent}
                            onChange={(e) => handleUpdateComboProduct(item.productId, 'discountPercent', Number(e.target.value))}
                            fullWidth
                          />
                        </FormItem>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
            
            {/* Tổng kết */}
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex justify-between items-center">
                <Typography variant="body2" className="font-medium">
                  {t('business:product.form.comboProduct.totalProducts', 'Tổng số sản phẩm')}:
                </Typography>
                <Typography variant="body2" className="font-medium">
                  {comboProducts.reduce((total, item) => total + item.quantity, 0)} sản phẩm
                </Typography>
              </div>
              <div className="flex justify-between items-center mt-1">
                <Typography variant="body2" className="font-medium">
                  {t('business:product.form.comboProduct.totalValue', 'Tổng giá trị')}:
                </Typography>
                <Typography variant="body2" className="font-medium text-blue-600 dark:text-blue-400">
                  {comboProducts.reduce((total, item) => {
                    const productData = loadedProducts.get(item.productId.toString());
                    const price = productData ? (productData.price?.salePrice || productData.price?.listPrice || 0) : 0;
                    return total + (price * item.quantity);
                  }, 0).toLocaleString('vi-VN')} VND
                </Typography>
              </div>
            </div>
          </div>
        )}
      </div>
    </CollapsibleCard>
  );
};

export default ComboProductsSection;
