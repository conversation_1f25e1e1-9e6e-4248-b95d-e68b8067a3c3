import React from 'react';
import { useTranslation } from 'react-i18next';
import { Controller } from 'react-hook-form';
import {
  CollapsibleCard,
  Typography,
  FormItem,
  Input,
  Select,
  Textarea,
  DateTimePicker,
} from '@/shared/components/common';
import { ServiceInfoSectionProps } from './service-product-form-types';

const ServiceInfoSection: React.FC<ServiceInfoSectionProps> = () => {
  const { t } = useTranslation(['business', 'common']);

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="font-medium">
          {t('business:product.form.sections.serviceInfo', '3. Thông tin dịch vụ')}
        </Typography>
      }
      defaultOpen={true}
      className="mb-4"
    >
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="serviceTime" label={t('business:product.form.serviceProduct.serviceTime')}>
            <Controller
              name="serviceTime"
              render={({ field }) => (
                <DateTimePicker
                  value={field.value}
                  onChange={field.onChange}
                  placeholder={t('business:product.form.serviceProduct.serviceTimePlaceholder')}
                  format="dd/MM/yyyy HH:mm"
                  timeFormat="24h"
                  fullWidth
                  minDate={new Date()}
                />
              )}
            />
          </FormItem>

          <FormItem name="serviceDuration" label={t('business:product.form.serviceProduct.serviceDuration')}>
            <Input
              fullWidth
              type="number"
              min="1"
              placeholder={t('business:product.form.serviceProduct.serviceDurationPlaceholder')}
            />
          </FormItem>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="serviceType" label={t('business:product.form.serviceProduct.serviceType.title')} required>
            <Select
              fullWidth
              options={[
                { value: 'CONSULTATION', label: t('business:product.form.serviceProduct.serviceType.consultation') },
                { value: 'BEAUTY', label: t('business:product.form.serviceProduct.serviceType.beauty') },
                { value: 'MAINTENANCE', label: t('business:product.form.serviceProduct.serviceType.maintenance') },
                { value: 'INSTALLATION', label: t('business:product.form.serviceProduct.serviceType.installation') },
              ]}
            />
          </FormItem>

          <FormItem name="serviceLocation" label={t('business:product.form.serviceProduct.serviceLocation.title')} required>
            <Select
              fullWidth
              options={[
                { value: 'AT_HOME', label: t('business:product.form.serviceProduct.serviceLocation.atHome') },
                { value: 'AT_CENTER', label: t('business:product.form.serviceProduct.serviceLocation.atCenter') },
                { value: 'ONLINE', label: t('business:product.form.serviceProduct.serviceLocation.online') },
              ]}
            />
          </FormItem>
        </div>

        <FormItem name="serviceProvider" label={t('business:product.form.serviceProduct.serviceProvider')}>
          <Input
            fullWidth
            placeholder={t('business:product.form.serviceProduct.serviceProviderPlaceholder')}
          />
        </FormItem>

        <FormItem name="description" label={t('business:product.form.description')}>
          <Textarea
            fullWidth
            rows={4}
            placeholder={t('business:product.form.serviceDescriptionPlaceholder')}
          />
        </FormItem>
      </div>
    </CollapsibleCard>
  );
};

export default ServiceInfoSection;
