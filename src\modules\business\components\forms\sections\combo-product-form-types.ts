import { FileWithMetadata } from '@/shared/types/file.types';
import { PriceTypeEnum, ShipmentConfigDto, ProductDto } from '@/modules/business/types/product.types';

// Interface cho trường tùy chỉnh đã chọn
export interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Interface cho combo product item
export interface ComboProductItem {
  productId: number;
  productName: string;
  quantity: number;
  discountPercent?: number;
  originalPrice?: number; // Giá gốc của sản phẩm
}

// Interface cho form values
export interface ComboProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string | number;
  salePrice?: string | number;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  // Shipping configuration (giữ nguyên từ ProductForm)
  shipmentConfig?: ShipmentConfigDto;
  // Combo product specific fields
  comboProducts: ComboProductItem[];
}

// Props cho các section components
export interface ComboGeneralInfoSectionProps {
  tempTags: string[];
  setTempTags: (tags: string[]) => void;
}

export interface ComboPricingSectionProps {
  comboProducts: ComboProductItem[];
  loadedProducts: Map<string, ProductDto>; // ProductDto map
  getProductPrice: (product: ProductDto) => number;
}

export interface ComboProductsSectionProps {
  comboProducts: ComboProductItem[];
  setComboProducts: (products: ComboProductItem[]) => void;
  loadedProducts: Map<string, ProductDto>;
  setLoadedProducts: (products: Map<string, ProductDto>) => void;
  handleAddComboProduct: (productId: number, productName: string) => void;
  handleRemoveComboProduct: (productId: number) => void;
  handleUpdateComboProduct: (productId: number, field: keyof ComboProductItem, value: string | number) => void;
  loadProducts: (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => Promise<{
    items: Array<{
      value: string;
      label: string;
      subtitle: string;
      data: Record<string, unknown>;
    }>;
    totalItems: number;
    totalPages: number;
    currentPage: number;
  }>;
}

export interface ComboMediaSectionProps {
  mediaFiles: FileWithMetadata[];
  setMediaFiles: (files: FileWithMetadata[]) => void;
  tempTags: string[];
  setTempTags: (tags: string[]) => void;
}

export interface ComboCustomFieldsSectionProps {
  productCustomFields: SelectedCustomField[];
  setProductCustomFields: (fields: SelectedCustomField[]) => void;
  handleToggleCustomFieldToProduct: (fieldId: number, fieldData?: Record<string, unknown>) => void;
  handleUpdateCustomFieldInProduct: (customFieldId: number, value: string | number | boolean) => void;
  handleRemoveCustomFieldFromProduct: (customFieldId: number) => void;
}
