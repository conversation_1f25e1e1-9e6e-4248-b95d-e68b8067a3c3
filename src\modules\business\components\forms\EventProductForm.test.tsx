import React from 'react';
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { I18nextProvider } from 'react-i18next';
import i18n from '@/shared/i18n';
import EventProductForm from './EventProductForm';

// Mock the hooks
jest.mock('../../hooks/useCustomFieldQuery', () => ({
  useCustomFields: () => ({ data: [], isLoading: false }),
}));

jest.mock('../../hooks/useProductImageUpload', () => ({
  useProductImageUpload: () => ({
    uploadProductImages: jest.fn(),
  }),
}));

jest.mock('../../hooks/useProductQuery', () => ({
  useCreateEventProduct: () => ({
    mutateAsync: jest.fn(),
    isPending: false,
  }),
  PRODUCT_QUERY_KEYS: {
    lists: () => ['products'],
  },
}));

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = createTestQueryClient();
  
  return (
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n}>
        {children}
      </I18nextProvider>
    </QueryClientProvider>
  );
};

describe('EventProductForm', () => {
  const mockProps = {
    onSuccess: jest.fn(),
    onCancel: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all sections correctly', () => {
    render(
      <TestWrapper>
        <EventProductForm {...mockProps} />
      </TestWrapper>
    );

    // Check if all sections are rendered
    expect(screen.getByText(/thông tin chung/i)).toBeInTheDocument();
    expect(screen.getByText(/thông tin sự kiện/i)).toBeInTheDocument();
    expect(screen.getByText(/loại vé/i)).toBeInTheDocument();
    expect(screen.getByText(/hình ảnh sự kiện/i)).toBeInTheDocument();
    expect(screen.getByText(/trường tùy chỉnh/i)).toBeInTheDocument();
  });

  it('renders form fields correctly', () => {
    render(
      <TestWrapper>
        <EventProductForm {...mockProps} />
      </TestWrapper>
    );

    // Check if main form fields are present
    expect(screen.getByLabelText(/tên/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/mô tả/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/hình thức tham dự/i)).toBeInTheDocument();
  });

  it('renders action buttons correctly', () => {
    render(
      <TestWrapper>
        <EventProductForm {...mockProps} />
      </TestWrapper>
    );

    // Check if action buttons are present
    expect(screen.getByTitle(/hủy/i)).toBeInTheDocument();
    expect(screen.getByTitle(/tạo sự kiện/i)).toBeInTheDocument();
  });

  it('renders default ticket type', () => {
    render(
      <TestWrapper>
        <EventProductForm {...mockProps} />
      </TestWrapper>
    );

    // Check if default ticket type is rendered
    expect(screen.getByText(/loại vé #1/i)).toBeInTheDocument();
    expect(screen.getByText(/thêm loại vé/i)).toBeInTheDocument();
  });

  it('shows conditional fields based on attendance mode', () => {
    render(
      <TestWrapper>
        <EventProductForm {...mockProps} />
      </TestWrapper>
    );

    // Default should be OFFLINE mode, so location field should be visible
    expect(screen.getByLabelText(/địa điểm sự kiện/i)).toBeInTheDocument();
  });
});
