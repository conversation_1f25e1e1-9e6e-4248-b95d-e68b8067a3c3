import { FileWithMetadata } from '@/shared/types/file.types';
import { PriceTypeEnum, DigitalProductConfig } from '@/modules/business/types/product.types';

// Interface cho trường tùy chỉnh đã chọn
export interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Extended FileWithMetadata interface for version images
export interface ExtendedFileWithMetadata extends FileWithMetadata {
  url?: string | undefined;
  name?: string | undefined;
  size?: number | undefined;
  type?: string | undefined;
  file: File; // Đảm bảo có property file
}

// Interface cho phiên bản sản phẩm số trong form
export interface FormDigitalProductVersion {
  id: string; // ID tạm thời cho quản lý state
  name: string;
  price: number;
  currency?: string;
  description?: string;
  quantity?: number;
  sku?: string;
  minQuantityPerPurchase?: number;
  maxQuantityPerPurchase?: number;
  images?: ExtendedFileWithMetadata[]; // Nhiều ảnh cho phiên bản
  customFields: SelectedCustomField[];
}

// Interface cho form values
export interface DigitalProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string;
  salePrice?: string;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  // Digital product specific fields
  deliveryMethod: DigitalProductConfig['digitalFulfillmentFlow']['deliveryMethod'];
  deliveryTiming: DigitalProductConfig['digitalFulfillmentFlow']['deliveryTiming'];
  digitalProductType: DigitalProductConfig['digitalOutput']['outputType'];
  accessLink?: string;
  usageInstructions?: string;
  // Phiên bản
  versions: FormDigitalProductVersion[];
}

// Props cho các section components
export interface DigitalGeneralInfoSectionProps {
  tempTags: string[];
  setTempTags: (tags: string[]) => void;
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface DigitalPricingSectionProps {}

export interface DigitalMediaSectionProps {
  mediaFiles: FileWithMetadata[];
  setMediaFiles: (files: FileWithMetadata[]) => void;
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface DigitalProcessingSectionProps {}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface DigitalOutputSectionProps {}

export interface DigitalVersionsSectionProps {
  versions: FormDigitalProductVersion[];
  setVersions: (versions: FormDigitalProductVersion[]) => void;
  handleAddVersion: () => void;
  handleRemoveVersion: (versionId: string) => void;
  handleUpdateVersion: (versionId: string, field: keyof FormDigitalProductVersion, value: string | number) => void;
  handleVersionImagesChange: (versionId: string, files: ExtendedFileWithMetadata[]) => void;
  handleToggleCustomFieldToVersion: (versionId: string, fieldId: number, fieldData?: Record<string, unknown>) => void;
  handleUpdateCustomFieldInVersion: (versionId: string, customFieldId: number, value: string) => void;
  handleRemoveCustomFieldFromVersion: (versionId: string, customFieldId: number) => void;
}

export interface DigitalCustomFieldsSectionProps {
  productCustomFields: SelectedCustomField[];
  setProductCustomFields: (fields: SelectedCustomField[]) => void;
  handleToggleCustomFieldToProduct: (fieldId: number, fieldData?: Record<string, unknown>) => void;
  handleUpdateCustomFieldInProduct: (customFieldId: number, value: string) => void;
  handleRemoveCustomFieldFromProduct: (customFieldId: number) => void;
}
