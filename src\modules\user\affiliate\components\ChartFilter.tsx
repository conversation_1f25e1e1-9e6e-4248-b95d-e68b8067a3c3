import React from 'react';
import { useTranslation } from 'react-i18next';
import { Select, ResponsiveGrid, Typography } from '@/shared/components/common';
import DoubleDatePicker from '@/shared/components/common/DoubleDatePicker/DoubleDatePicker';

export interface ChartFilterData {
  /**
   * <PERSON><PERSON><PERSON>ng thời gian [startDate, endDate]
   */
  dateRange: [Date | null, Date | null];
  
  /**
   * Loại dữ liệu: commission, orders, customers
   */
  dataType: string;
}

export interface ChartFilterProps {
  /**
   * Giá trị filter hiện tại
   */
  value: ChartFilterData;
  
  /**
   * Callback khi filter thay đổi
   */
  onChange: (filter: ChartFilterData) => void;
  
  /**
   * Class CSS bổ sung
   */
  className?: string;
}

/**
 * Component bộ lọc cho biểu đồ affiliate
 */
const ChartFilter: React.FC<ChartFilterProps> = ({
  value,
  onChange,
  className = '',
}) => {
  const { t } = useTranslation(['userAffiliate']);

  // Options cho loại dữ liệu
  const dataTypeOptions = [
    {
      value: 'commission',
      label: t('userAffiliate:chartFilter.dataTypes.commission'),
    },
    {
      value: 'orders',
      label: t('userAffiliate:chartFilter.dataTypes.orders'),
    },
    {
      value: 'customers',
      label: t('userAffiliate:chartFilter.dataTypes.customers'),
    },
  ];

  const handleDateRangeChange = (dateRange: [Date | null, Date | null]) => {
    onChange({
      ...value,
      dateRange,
    });
  };

  const handleDataTypeChange = (dataType: string | string[] | number | number[]) => {
    onChange({
      ...value,
      dataType: dataType as string,
    });
  };

  return (
    <div className={`mb-4 ${className}`}>
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }} gap={4}>
        <div>
          <div className="flex items-center gap-2">
            <DoubleDatePicker
              value={value.dateRange}
              onChange={handleDateRangeChange}
              size="md"
              className="border border-input"
              noBorder={false}
            />
            <Typography variant="body2" className="text-muted-foreground">
              {value.dateRange[0] && value.dateRange[1]
                ? `${value.dateRange[0].toLocaleDateString('vi-VN')} - ${value.dateRange[1].toLocaleDateString('vi-VN')}`
                : t('userAffiliate:chartFilter.selectTimeRange')
              }
            </Typography>
          </div>
        </div>

        <div>
          <Select
            value={value.dataType}
            onChange={handleDataTypeChange}
            options={dataTypeOptions}
            placeholder={t('userAffiliate:chartFilter.selectDataType')}
            fullWidth
          />
        </div>
      </ResponsiveGrid>
    </div>
  );
};

export default ChartFilter;
