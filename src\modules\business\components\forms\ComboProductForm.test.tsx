import React from 'react';
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { I18nextProvider } from 'react-i18next';
import i18n from '@/shared/i18n';
import ComboProductForm from './ComboProductForm';

// Mock the hooks
jest.mock('../../hooks/useCustomFieldQuery', () => ({
  useCustomFields: () => ({ data: [], isLoading: false }),
}));

jest.mock('../../hooks/useProductImageUpload', () => ({
  useProductImageUpload: () => ({
    uploadProductImages: jest.fn(),
  }),
}));

jest.mock('../../services/product.service', () => ({
  ProductService: {
    getProducts: jest.fn().mockResolvedValue({
      items: [],
      meta: { totalItems: 0, totalPages: 0, currentPage: 1 },
    }),
  },
}));

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = createTestQueryClient();
  
  return (
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n}>
        {children}
      </I18nextProvider>
    </QueryClientProvider>
  );
};

describe('ComboProductForm', () => {
  const mockProps = {
    onSubmit: jest.fn(),
    onCancel: jest.fn(),
    isSubmitting: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all sections correctly', () => {
    render(
      <TestWrapper>
        <ComboProductForm {...mockProps} />
      </TestWrapper>
    );

    // Check if all sections are rendered
    expect(screen.getByText(/thông tin chung/i)).toBeInTheDocument();
    expect(screen.getByText(/sản phẩm trong combo/i)).toBeInTheDocument();
    expect(screen.getByText(/giá combo/i)).toBeInTheDocument();
    expect(screen.getByText(/hình ảnh combo/i)).toBeInTheDocument();
    expect(screen.getByText(/trường tùy chỉnh/i)).toBeInTheDocument();
  });

  it('renders form fields correctly', () => {
    render(
      <TestWrapper>
        <ComboProductForm {...mockProps} />
      </TestWrapper>
    );

    // Check if main form fields are present
    expect(screen.getByLabelText(/tên/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/mô tả/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/giá bán combo/i)).toBeInTheDocument();
  });

  it('renders action buttons correctly', () => {
    render(
      <TestWrapper>
        <ComboProductForm {...mockProps} />
      </TestWrapper>
    );

    // Check if action buttons are present
    expect(screen.getByTitle(/hủy/i)).toBeInTheDocument();
    expect(screen.getByTitle(/tạo combo/i)).toBeInTheDocument();
  });

  it('disables submit button when no products in combo', () => {
    render(
      <TestWrapper>
        <ComboProductForm {...mockProps} />
      </TestWrapper>
    );

    const submitButton = screen.getByTitle(/tạo combo/i);
    expect(submitButton).toBeDisabled();
  });

  it('shows empty state for combo products', () => {
    render(
      <TestWrapper>
        <ComboProductForm {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText(/chưa có sản phẩm nào trong combo/i)).toBeInTheDocument();
  });
});
