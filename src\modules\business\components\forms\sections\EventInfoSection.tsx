import React from 'react';
import { useTranslation } from 'react-i18next';
import { Controller } from 'react-hook-form';
import {
  CollapsibleCard,
  Typography,
  FormItem,
  Input,
  Select,
  DateTimePicker,
  ConditionalField,
} from '@/shared/components/common';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { EventInfoSectionProps } from './event-product-form-types';

const EventInfoSection: React.FC<EventInfoSectionProps> = () => {
  const { t } = useTranslation(['business', 'common']);

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="font-medium">
          {t('business:product.form.sections.eventInfo', '2. Thông tin sự kiện')}
        </Typography>
      }
      defaultOpen={true}
      className="mb-4"
    >
      <div className="space-y-4">
        {/* Thời gian sự kiện */}
        <FormItem name="eventDateTime" label={t('business:product.form.eventProduct.eventDateTime', 'Thời gian sự kiện')}>
          <Controller
            name="eventDateTime"
            render={({ field }) => (
              <DateTimePicker
                value={field.value}
                onChange={field.onChange}
                placeholder={t('business:product.form.eventProduct.eventDateTimePlaceholder', 'Chọn thời gian sự kiện')}
                fullWidth
              />
            )}
          />
        </FormItem>

        {/* Hình thức tham dự */}
        <FormItem 
          name="attendanceMode" 
          label={t('business:product.form.eventProduct.attendanceMode', 'Hình thức tham dự')} 
          required
        >
          <Controller
            name="attendanceMode"
            render={({ field }) => (
              <Select
                fullWidth
                value={field.value || 'OFFLINE'}
                onChange={value => field.onChange(value)}
                options={[
                  { 
                    value: 'ONLINE', 
                    label: t('business:product.form.eventProduct.attendanceMode.online', 'Trực tuyến') 
                  },
                  { 
                    value: 'OFFLINE', 
                    label: t('business:product.form.eventProduct.attendanceMode.offline', 'Tại địa điểm') 
                  },
                ]}
              />
            )}
          />
        </FormItem>

        {/* Địa điểm sự kiện - hiển thị khi chọn OFFLINE */}
        <ConditionalField
          name="attendanceMode"
          condition={ConditionType.EQUALS}
          value="OFFLINE"
        >
          <FormItem 
            name="eventLocation" 
            label={t('business:product.form.eventProduct.eventLocation', 'Địa điểm sự kiện')} 
            required
          >
            <Input
              fullWidth
              placeholder={t('business:product.form.eventProduct.eventLocationPlaceholder', 'Nhập địa điểm tổ chức sự kiện')}
            />
          </FormItem>
        </ConditionalField>

        {/* Link Zoom - hiển thị khi chọn ONLINE */}
        <ConditionalField
          name="attendanceMode"
          condition={ConditionType.EQUALS}
          value="ONLINE"
        >
          <FormItem 
            name="zoomLink" 
            label={t('business:product.form.eventProduct.zoomLink', 'Link Zoom/Meeting')}
          >
            <Input
              fullWidth
              type="url"
              placeholder={t('business:product.form.eventProduct.zoomLinkPlaceholder', 'https://zoom.us/j/...')}
            />
          </FormItem>
        </ConditionalField>

        {/* Thông tin bổ sung */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <Typography variant="body2" className="text-blue-800 dark:text-blue-200 mb-2 font-medium">
            {t('business:product.form.eventProduct.infoNote', 'Lưu ý về thông tin sự kiện')}
          </Typography>
          <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
            <li>• {t('business:product.form.eventProduct.infoNote.time', 'Thời gian sự kiện sẽ được sử dụng cho tất cả loại vé')}</li>
            <li>• {t('business:product.form.eventProduct.infoNote.location', 'Địa điểm bắt buộc cho sự kiện offline')}</li>
            <li>• {t('business:product.form.eventProduct.infoNote.link', 'Link meeting bắt buộc cho sự kiện online')}</li>
          </ul>
        </div>
      </div>
    </CollapsibleCard>
  );
};

export default EventInfoSection;
