import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Controller } from 'react-hook-form';
import {
  CollapsibleCard,
  Typography,
  FormItem,
  Input,
  Select,
} from '@/shared/components/common';
import { PriceTypeEnum } from '@/modules/business/types/product.types';
import { ComboPricingSectionProps } from './combo-product-form-types';

const ComboPricingSection: React.FC<ComboPricingSectionProps> = ({
  comboProducts,
  loadedProducts,
  getProductPrice,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Tính tổng giá niêm yết của combo
  const calculateComboListPrice = useMemo((): number => {
    return comboProducts.reduce((total, item) => {
      const productData = loadedProducts.get(item.productId.toString());
      const originalPrice = productData ? getProductPrice(productData) : 0;
      return total + (originalPrice * item.quantity);
    }, 0);
  }, [comboProducts, loadedProducts, getProductPrice]);

  // Tính tổng tiết kiệm
  const calculateSavings = useMemo(() => {
    return (salePrice: number) => {
      const listPrice = calculateComboListPrice;
      return listPrice > salePrice ? listPrice - salePrice : 0;
    };
  }, [calculateComboListPrice]);

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="font-medium">
          {t('business:product.form.sections.comboPricing', '2. Giá combo')}
        </Typography>
      }
      defaultOpen={true}
      className="mb-4"
    >
      <div className="space-y-4">
        {/* Hiển thị thông tin tính toán giá */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <Typography variant="body2" className="font-medium text-blue-800 dark:text-blue-200 mb-2">
            {t('business:product.form.comboProduct.priceCalculation', 'Tính toán giá combo')}
          </Typography>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">
                {t('business:product.form.comboProduct.totalOriginalPrice', 'Tổng giá gốc')}:
              </span>
              <span className="font-medium">
                {calculateComboListPrice.toLocaleString('vi-VN')} VND
              </span>
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {comboProducts.length > 0 ? (
                <div>
                  {comboProducts.map((item) => {
                    const productData = loadedProducts.get(item.productId.toString());
                    const price = productData ? getProductPrice(productData) : 0;
                    return (
                      <div key={item.productId} className="flex justify-between">
                        <span>
                          {item.productName} x{item.quantity}:
                        </span>
                        <span>{(price * item.quantity).toLocaleString('vi-VN')} VND</span>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <span>{t('business:product.form.comboProduct.noProductsSelected', 'Chưa chọn sản phẩm nào')}</span>
              )}
            </div>
          </div>
        </div>

        {/* Giá bán combo */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="salePrice" label={t('business:product.form.comboProduct.comboPrice', 'Giá bán combo')} required>
            <Controller
              name="salePrice"
              render={({ field }) => (
                <div>
                  <Input
                    fullWidth
                    type="number"
                    min="0"
                    placeholder={t('business:product.enterSalePrice')}
                    value={field.value}
                    onChange={(e) => field.onChange(e.target.value)}
                  />
                  {field.value && calculateComboListPrice > 0 && (
                    <div className="mt-2 text-sm">
                      <div className="flex justify-between text-green-600 dark:text-green-400">
                        <span>{t('business:product.form.comboProduct.savings', 'Tiết kiệm')}:</span>
                        <span className="font-medium">
                          {calculateSavings(Number(field.value)).toLocaleString('vi-VN')} VND
                        </span>
                      </div>
                      <div className="flex justify-between text-green-600 dark:text-green-400">
                        <span>{t('business:product.form.comboProduct.discountPercent', 'Giảm giá')}:</span>
                        <span className="font-medium">
                          {calculateComboListPrice > 0 
                            ? Math.round((calculateSavings(Number(field.value)) / calculateComboListPrice) * 100)
                            : 0
                          }%
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              )}
            />
          </FormItem>

          <FormItem name="currency" label={t('business:product.currency')} required>
            <Controller
              name="currency"
              render={({ field }) => (
                <Select
                  fullWidth
                  value={field.value || 'VND'}
                  onChange={value => field.onChange(value)}
                  options={[
                    { value: 'VND', label: 'VND' },
                    { value: 'USD', label: 'USD' },
                    { value: 'EUR', label: 'EUR' },
                  ]}
                />
              )}
            />
          </FormItem>
        </div>

        {/* Thông tin bổ sung */}
        {calculateComboListPrice > 0 && (
          <div className="bg-gray-50 dark:bg-gray-800/30 p-3 rounded-lg">
            <Typography variant="caption" className="text-gray-600 dark:text-gray-400">
              {t('business:product.form.comboProduct.priceNote',
                'Lưu ý: Giá niêm yết sẽ được tự động tính từ tổng giá các sản phẩm trong combo. Giá bán phải nhỏ hơn giá niêm yết để tạo ưu đãi cho khách hàng.'
              )}
            </Typography>
          </div>
        )}

        {/* Hidden field để form validation */}
        <div className="hidden">
          <FormItem name="typePrice">
            <Input value={PriceTypeEnum.HAS_PRICE} readOnly />
          </FormItem>
          <FormItem name="listPrice">
            <Input value={calculateComboListPrice} readOnly />
          </FormItem>
        </div>
      </div>
    </CollapsibleCard>
  );
};

export default ComboPricingSection;
