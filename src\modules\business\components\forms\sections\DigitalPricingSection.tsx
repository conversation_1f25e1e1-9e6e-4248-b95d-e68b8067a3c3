import React from 'react';
import { useTranslation } from 'react-i18next';
import { Controller } from 'react-hook-form';
import {
  CollapsibleCard,
  Typography,
  FormItem,
  Input,
  Select,
  ConditionalField,
} from '@/shared/components/common';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { PriceTypeEnum } from '@/modules/business/types/product.types';
import { DigitalPricingSectionProps } from './digital-product-form-types';

const DigitalPricingSection: React.FC<DigitalPricingSectionProps> = () => {
  const { t } = useTranslation(['business', 'common']);

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="font-medium">
          {t('business:product.form.sections.pricing', '2. Giá sản phẩm')}
        </Typography>
      }
      defaultOpen={true}
      className="mb-4"
    >
      <div className="space-y-4">
        <FormItem name="typePrice" label={t('business:product.priceType.title')} required>
          <Select
            fullWidth
            options={[
              {
                value: PriceTypeEnum.HAS_PRICE,
                label: t('business:product.priceType.hasPrice'),
              },
              {
                value: PriceTypeEnum.STRING_PRICE,
                label: t('business:product.priceType.stringPrice'),
              },
            ]}
          />
        </FormItem>

        <ConditionalField
          condition={{
            field: 'typePrice',
            type: ConditionType.EQUALS,
            value: PriceTypeEnum.HAS_PRICE,
          }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem name="listPrice" label={t('business:product.listPrice')} required>
              <Input fullWidth type="number" min="0" placeholder={t('business:product.enterListPrice')} />
            </FormItem>
            <FormItem name="salePrice" label={t('business:product.salePrice')} required>
              <Input fullWidth type="number" min="0" placeholder={t('business:product.enterSalePrice')} />
            </FormItem>
            <FormItem name="currency" label={t('business:product.currency')} required>
              <Controller
                name="currency"
                render={({ field }) => (
                  <Select
                    fullWidth
                    value={field.value || 'VND'}
                    onChange={value => field.onChange(value)}
                    options={[
                      { value: 'VND', label: 'VND' },
                      { value: 'USD', label: 'USD' },
                      { value: 'EUR', label: 'EUR' },
                    ]}
                  />
                )}
              />
            </FormItem>
          </div>
        </ConditionalField>

        <ConditionalField
          condition={{
            field: 'typePrice',
            type: ConditionType.EQUALS,
            value: PriceTypeEnum.STRING_PRICE,
          }}
        >
          <FormItem
            name="priceDescription"
            label={t('business:product.priceDescription')}
            required
          >
            <Input
              fullWidth
              placeholder={t('business:product.form.priceDescriptionPlaceholder')}
            />
          </FormItem>
        </ConditionalField>
      </div>
    </CollapsibleCard>
  );
};

export default DigitalPricingSection;
