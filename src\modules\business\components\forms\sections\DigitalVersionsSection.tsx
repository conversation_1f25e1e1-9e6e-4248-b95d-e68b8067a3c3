import React from 'react';
import { useTranslation } from 'react-i18next';
import { Controller } from 'react-hook-form';
import {
  CollapsibleCard,
  Typography,
  FormItem,
  Input,
  Select,
  Textarea,
  IconCard,
} from '@/shared/components/common';
import MultiFileUpload from '@/modules/data/components/MultiFileUpload';
import SimpleCustomFieldSelector from '@/modules/business/components/SimpleCustomFieldSelector';
import CustomFieldRenderer from '@/modules/business/components/CustomFieldRenderer';
import { DigitalVersionsSectionProps, ExtendedFileWithMetadata } from './digital-product-form-types';

const DigitalVersionsSection: React.FC<DigitalVersionsSectionProps> = ({
  versions,
  handleAddVersion,
  handleRemoveVersion,
  handleUpdateVersion,
  handleVersionImagesChange,
  handleToggleCustomFieldToVersion,
  handleUpdateCustomFieldInVersion,
  handleRemoveCustomFieldFromVersion,
}) => {
  const { t } = useTranslation(['business', 'common']);

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="font-medium">
            {versions.length > 0
              ? `6. ${t('business:product.form.versions.title', 'Phiên bản')} (${versions.length})`
              : `6. ${t('business:product.form.versions.title', 'Phiên bản')}`
            }
          </Typography>
          <div
            onClick={e => {
              e.stopPropagation();
              handleAddVersion();
            }}
            className="cursor-pointer"
          >
            <IconCard
              icon="plus"
              title={t('business:product.form.versions.addVersion', 'Thêm phiên bản')}
              variant="primary"
              size="sm"
            />
          </div>
        </div>
      }
      defaultOpen={true}
      className="mb-4"
    >
      <div className="space-y-4">
        {versions.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <Typography variant="body2">
              {t('business:product.form.versions.noVersions', 'Chưa có phiên bản nào. Nhấn "Thêm phiên bản" để bắt đầu.')}
            </Typography>
          </div>
        ) : (
          <div className="space-y-4">
            {versions.map((version, index) => (
              <CollapsibleCard
                key={version.id}
                title={
                  <div className="flex justify-between items-center w-full">
                    <div className="flex items-center space-x-4">
                      <Typography variant="body2" className="font-medium">
                        {version.name || `Phiên bản ${index + 1}`}
                      </Typography>
                      <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                        {version.price > 0 ? `${version.price.toLocaleString()} ${version.currency}` : '0 VND'}
                      </Typography>
                      <Typography variant="body2" className="text-gray-500 dark:text-gray-500">
                        {version.quantity} bản
                      </Typography>
                      {version.id && (
                        <Typography variant="body2" className="text-gray-400 dark:text-gray-600 text-xs">
                          ID: {version.id.split('-').pop()}
                        </Typography>
                      )}
                    </div>
                    <div
                      onClick={e => {
                        e.stopPropagation();
                        handleRemoveVersion(version.id!);
                      }}
                      className="cursor-pointer"
                    >
                      <IconCard
                        icon="trash"
                        title={t('business:product.form.versions.removeVersion', 'Xóa phiên bản')}
                        variant="danger"
                        size="sm"
                      />
                    </div>
                  </div>
                }
                defaultOpen={true}
              >
                <div className="space-y-4">
                  {/* Tên phiên bản và giá */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormItem label={t('business:product.form.versions.name', 'Tên phiên bản')} required>
                      <Input
                        fullWidth
                        placeholder={t('business:product.form.versions.namePlaceholder', 'Basic, Pro, Premium...')}
                        value={version.name}
                        onChange={(e) => handleUpdateVersion(version.id!, 'name', e.target.value)}
                      />
                    </FormItem>
                    <FormItem label={t('business:product.form.versions.price', 'Giá')} required>
                      <Input
                        fullWidth
                        type="number"
                        min="0"
                        placeholder="0"
                        value={version.price}
                        onChange={(e) => handleUpdateVersion(version.id!, 'price', Number(e.target.value))}
                      />
                    </FormItem>
                    <FormItem label={t('business:product.form.versions.currency', 'Đơn vị tiền tệ')} required>
                      <Select
                        fullWidth
                        value={version.currency || 'VND'}
                        onChange={(value) => {
                          const selectedValue = Array.isArray(value) ? value[0] : value;
                          if (selectedValue !== undefined) {
                            handleUpdateVersion(version.id!, 'currency', selectedValue);
                          }
                        }}
                        options={[
                          { value: 'VND', label: 'VND' },
                          { value: 'USD', label: 'USD' },
                          { value: 'EUR', label: 'EUR' },
                        ]}
                      />
                    </FormItem>
                  </div>

                  {/* Mô tả phiên bản */}
                  <FormItem label={t('business:product.form.versions.description', 'Mô tả phiên bản')}>
                    <Textarea
                      fullWidth
                      rows={3}
                      placeholder={t('business:product.form.versions.descriptionPlaceholder', 'Mô tả chi tiết về phiên bản này...')}
                      value={version.description || ''}
                      onChange={(e) => handleUpdateVersion(version.id!, 'description', e.target.value)}
                    />
                  </FormItem>

                  {/* Số lượng và SKU */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItem label={t('business:product.form.versions.quantity', 'Số lượng có sẵn')} required>
                      <Input
                        fullWidth
                        type="number"
                        min="1"
                        placeholder="100"
                        value={version.quantity}
                        onChange={(e) => handleUpdateVersion(version.id!, 'quantity', Number(e.target.value))}
                      />
                    </FormItem>
                    <FormItem label={t('business:product.form.versions.sku', 'Mã SKU')}>
                      <Input
                        fullWidth
                        placeholder={t('business:product.form.versions.skuPlaceholder', 'BASIC-001')}
                        value={version.sku || ''}
                        onChange={(e) => handleUpdateVersion(version.id!, 'sku', e.target.value)}
                      />
                    </FormItem>
                  </div>

                  {/* Số lượng mua tối thiểu và tối đa */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItem label={t('business:product.form.versions.minQuantity', 'Số lượng tối thiểu mỗi lần mua')}>
                      <Input
                        fullWidth
                        type="number"
                        min="1"
                        placeholder="1"
                        value={version.minQuantityPerPurchase}
                        onChange={(e) => handleUpdateVersion(version.id!, 'minQuantityPerPurchase', Number(e.target.value))}
                      />
                    </FormItem>
                    <FormItem label={t('business:product.form.versions.maxQuantity', 'Số lượng tối đa mỗi lần mua')}>
                      <Input
                        fullWidth
                        type="number"
                        min="1"
                        placeholder="10"
                        value={version.maxQuantityPerPurchase}
                        onChange={(e) => handleUpdateVersion(version.id!, 'maxQuantityPerPurchase', Number(e.target.value))}
                      />
                    </FormItem>
                  </div>

                  {/* Ảnh phiên bản */}
                  <FormItem label={t('business:product.form.versions.images', 'Ảnh phiên bản')}>
                    <MultiFileUpload
                      value={version.images || []}
                      onChange={(files: ExtendedFileWithMetadata[]) => {
                        handleVersionImagesChange(version.id!, files);
                      }}
                      accept="image/*"
                      mediaOnly={true}
                      placeholder={t(
                        'business:product.form.versions.imagesPlaceholder',
                        'Kéo thả hoặc click để tải lên ảnh cho phiên bản này'
                      )}
                      className="w-full"
                    />
                  </FormItem>

                  {/* Trường tùy chỉnh cho phiên bản */}
                  <div className="space-y-3">
                    <Typography variant="body2" className="font-medium">
                      {t('business:product.form.versions.customFields', 'Trường tùy chỉnh cho phiên bản')}
                    </Typography>
                    
                    <SimpleCustomFieldSelector
                      onFieldSelect={fieldData => {
                        handleToggleCustomFieldToVersion(
                          version.id!,
                          fieldData.id,
                          fieldData as unknown as Record<string, unknown>
                        );
                      }}
                      selectedFieldIds={version.customFields.map(f => f.fieldId)}
                      placeholder={t(
                        'business:product.form.versions.customFieldsPlaceholder',
                        'Nhập từ khóa và nhấn Enter để tìm trường tùy chỉnh cho phiên bản...'
                      )}
                    />

                    {version.customFields.length > 0 && (
                      <div className="space-y-2">
                        {version.customFields.map(field => (
                          <CustomFieldRenderer
                            key={field.id}
                            field={field}
                            value={(field.value?.['value'] as string) || ''}
                            onChange={value => handleUpdateCustomFieldInVersion(version.id!, field.id, value as string)}
                            onRemove={() => handleRemoveCustomFieldFromVersion(version.id!, field.id)}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </CollapsibleCard>
            ))}
          </div>
        )}
      </div>
    </CollapsibleCard>
  );
};

export default DigitalVersionsSection;
